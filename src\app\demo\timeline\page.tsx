'use client';

import React, { useState } from 'react';
import BookingTimeline from '@/components/booking/BookingTimeline';

export default function TimelineDemo() {
  const [currentStatus, setCurrentStatus] = useState<'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled'>('pending');

  const statuses = [
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ] as const;

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Booking Timeline Demo
          </h1>
          <p className="text-gray-600 mb-6">
            Test the booking timeline component with different statuses
          </p>
          
          {/* Status Selector */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {statuses.map((status) => (
              <button
                key={status.value}
                onClick={() => setCurrentStatus(status.value)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  currentStatus === status.value
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {status.label}
              </button>
            ))}
          </div>
        </div>

        {/* Timeline Component */}
        <div className="max-w-4xl mx-auto">
          <BookingTimeline
            currentStatus={currentStatus}
            showCertificateButton={currentStatus === 'completed'}
            onShowCertificate={() => alert('Certificate would be shown here')}
          />
        </div>

        {/* Status Info */}
        <div className="mt-8 text-center">
          <div className="inline-block bg-white rounded-lg shadow-sm border border-gray-200 px-6 py-4">
            <p className="text-sm text-gray-600">
              Current Status: <span className="font-semibold text-gray-900 capitalize">{currentStatus.replace('_', ' ')}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
